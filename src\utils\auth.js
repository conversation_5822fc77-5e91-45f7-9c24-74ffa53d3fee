import Cookies from 'js-cookie'
import website from '@/config/website'
import store from '@/store'

const TokenKey = website.tokenKey
const RefreshTokenKey = website.refreshTokenKey
const SessionId = 'JSESSIONID'
const UserId = 'b-user-id'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey)
}

export function setRefreshToken(token) {
  return Cookies.set(RefreshTokenKey, token)
}

export function removeToken() {
  Cookies.remove(SessionId)
  Cookies.remove(UserId)
  return Cookies.remove(TokenKey)
}

export function removeRefreshToken() {
  return Cookies.remove(RefreshTokenKey)
}


/**
 * 检查用户是否拥有指定权限
 * @param {string|Array} permission - 权限码或权限码数组
 * @returns {boolean} - 是否有权限
 */
export function hasAuth(permission) {
  // 获取用户权限数据
  const userPermissions = store.getters.permission || {}

  // 参数验证
  if (!permission) {
    console.warn('[v-perms] 权限参数不能为空')
    return false
  }

  try {
    // 单个权限码字符串
    if (typeof permission === 'string') {
      return !!userPermissions[permission]
    }

    // 权限码数组（OR逻辑：只要有一个权限码匹配就返回true）
    if (Array.isArray(permission)) {
      if (permission.length === 0) {
        console.warn('[v-perms] 权限码数组不能为空')
        return false
      }
      return permission.some(code => !!userPermissions[code])
    }

    // 不支持的参数类型
    console.warn('[v-perms] 不支持的权限参数类型:', typeof permission)
    return false

  } catch (error) {
    console.error('[v-perms] 权限验证出错:', error)
    return false
  }
}
